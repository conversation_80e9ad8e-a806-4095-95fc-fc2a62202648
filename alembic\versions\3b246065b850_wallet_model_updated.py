"""wallet model updated

Revision ID: 3b246065b850
Revises: 04e586413b09
Create Date: 2025-05-26 12:55:55.763000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b246065b850'
down_revision: Union[str, None] = '04e586413b09'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('wallets', sa.Column('total_deposits', sa.Float(), nullable=True))
    op.add_column('wallets', sa.Column('current_deposits', sa.Float(), nullable=True))
    op.add_column('wallets', sa.Column('current_profit', sa.Float(), nullable=True))
    op.add_column('wallets', sa.Column('current_referral_bonus', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('wallets', 'current_referral_bonus')
    op.drop_column('wallets', 'current_profit')
    op.drop_column('wallets', 'current_deposits')
    op.drop_column('wallets', 'total_deposits')
    # ### end Alembic commands ###
