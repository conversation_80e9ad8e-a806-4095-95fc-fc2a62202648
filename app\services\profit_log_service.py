from sqlalchemy.orm import Session
from app.models.profit_logs import ProfitLog
from app.schemas.profit_logs import ProfitLogCreate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_profit_logs(db: Session, params: PaginationParams) -> Page[ProfitLog]:
    query = db.query(ProfitLog)
    return paginate(query, params)

def get_profit_log(db: Session, profit_log_id: int) -> Optional[ProfitLog]:
    return db.query(ProfitLog).filter(ProfitLog.id == profit_log_id).first()

def create_profit_log(db: Session, ProfitLog: ProfitLogCreate) -> ProfitLog:
    db_profit_log = ProfitLog(**ProfitLog.dict())
    db.add(db_profit_log)
    db.commit()
    db.refresh(db_profit_log)
    return db_profit_log

# def update_profit_log(db: Session, profit_log_id: int, ProfitLog: profit_logUpdate) -> Optional[ProfitLog]:
#     db_profit_log = get_profit_log(db, profit_log_id)
#     if db_profit_log:
#         update_data = ProfitLog.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_profit_log, key, value)
#         db.commit()
#         db.refresh(db_profit_log)
#     return db_profit_log

def delete_profit_log(db: Session, profit_log_id: int) -> bool:
    db_profit_log = get_profit_log(db, profit_log_id)
    if db_profit_log:
        db.delete(db_profit_log)
        db.commit()
        return True
    return False