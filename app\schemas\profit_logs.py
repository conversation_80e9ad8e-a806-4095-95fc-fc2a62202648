from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum




class ProfitLogBase(PydanticBaseModel):
    deposit_id: int
    user_id: int
    profit_amount: float
    profit_percent: float

class ProfitLogCreate(ProfitLogBase):
    pass

class ProfitLogOut(ProfitLogBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True