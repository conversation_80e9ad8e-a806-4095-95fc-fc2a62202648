from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.transaction_logs import TransactionLogCreate, TransactionLogOut
from app.services.transaction_log_service import get_transaction_logs, get_transaction_log, create_transaction_log, delete_transaction_log
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/transaction_logs",
    tags=["transaction_logs"]
)

@router.get("/", response_model=Page[TransactionLogOut])
def read_transaction_logs(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_transaction_logs(db, params=params)

@router.get("/{transaction_log_id}", response_model=TransactionLogOut)
def read_transaction_log(transaction_log_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    transaction_log = get_transaction_log(db, transaction_log_id=transaction_log_id)
    if transaction_log is None:
        raise HTTPException(status_code=404, detail="transaction_log not found")
    return transaction_log

@router.post("/", response_model=TransactionLogOut, status_code=status.HTTP_201_CREATED)
def create_new_transaction_log(transaction_log: TransactionLogCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_transaction_log(db=db, transaction_log=transaction_log)

# @router.put("/{transaction_log_id}", response_model=TransactionLogOut)
# def update_existing_transaction_log(transaction_log_id: int, transaction_log: TransactionLogUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
#     updated_transaction_log = update_transaction_log(db=db, transaction_log_id=transaction_log_id, transaction_log=transaction_log)
#     if updated_transaction_log is None:
#         raise HTTPException(status_code=404, detail="transaction_log not found")
#     return updated_transaction_log

@router.delete("/{transaction_log_id}", status_code=status.HTTP_200_OK)
def delete_existing_transaction_log(transaction_log_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_transaction_log(db=db, transaction_log_id=transaction_log_id)
    if not success:
        raise HTTPException(status_code=404, detail="transaction_log not found")
    return {"detail": "transaction_log deleted successfully"}