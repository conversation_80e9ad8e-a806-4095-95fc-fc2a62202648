from sqlalchemy.orm import Session
from app.models.withdrawals import Withdrawal
from app.schemas.withdrawals import WithdrawalCreate, WithdrawalUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from sqlalchemy.sql import func

def get_withdrawals(db: Session, params: PaginationParams) -> Page[Withdrawal]:
    query = db.query(Withdrawal)
    return paginate(query, params)

def get_withdrawal(db: Session, withdrawal_id: int) -> Optional[Withdrawal]:
    return db.query(Withdrawal).filter(Withdrawal.id == withdrawal_id).first()

def create_withdrawal(db: Session, withdrawal: WithdrawalCreate, user_id: int) -> Withdrawal:
    db_withdrawal = Withdrawal(
        user_id=user_id,
        amount=withdrawal.amount,
        wallet_address=withdrawal.wallet_address,
        network=withdrawal.network,
        status="pending")
    
    db.add(db_withdrawal)
    db.commit()
    db.refresh(db_withdrawal)
    return db_withdrawal

# def update_withdrawal(db: Session, withdrawal_id: int, withdrawal: WithdrawalUpdate) -> Optional[Withdrawal]:
#     db_withdrawal = get_withdrawal(db, withdrawal_id)
#     if db_withdrawal:
#         update_data = withdrawal.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_withdrawal, key, value)
#         db.commit()
#         db.refresh(db_withdrawal)
#     return db_withdrawal

def delete_withdrawal(db: Session, withdrawal_id: int) -> bool:
    db_withdrawal = get_withdrawal(db, withdrawal_id)
    if db_withdrawal:
        db.delete(db_withdrawal)
        db.commit()
        return True
    return False


def withdrawal_status_update(db: Session, withdrawal_id: int, withdrawal: WithdrawalUpdate) -> Optional[Withdrawal]:
    db_withdrawal = get_withdrawal(db, withdrawal_id)
    if db_withdrawal:
        if withdrawal.status.lower() == "approved":
            db_withdrawal.status = withdrawal.status
            db_withdrawal.approved_at = func.now()
        else:
            db_withdrawal.status = withdrawal.status
        db.commit()
        db.refresh(db_withdrawal)
        return db_withdrawal
