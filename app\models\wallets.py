from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey



# WALLETS Table
class Wallet(BaseModel):
    __tablename__ = "wallets"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    balance = Column(Float, default=0.0)
    total_deposits = Column(Float, default=0.0)
    total_profit = Column(Float, default=0.0)
    total_referral_bonus = Column(Float, default=0.0)
    current_deposits = Column(Float, default=0.0)
    current_profit = Column(Float, default=0.0)
    current_referral_bonus = Column(Float, default=0.0)
    
    user = relationship("User", back_populates="wallet")