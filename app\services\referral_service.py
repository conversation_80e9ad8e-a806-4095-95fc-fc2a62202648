from sqlalchemy.orm import Session
from sqlalchemy import text, select
from typing import Dict, Optional, Any, List
import decimal

from app.models.users import User
from app.models.deposits import Deposit
from app.schemas.deposits import DepositUpdate
from app.models.wallets import Wallet
from app.models.transaction_logs import TransactionLedger, TransactionType
from app.models.referral_percentage import ReferralLevelPercentage

from app.models.referral_commision import ReferralCommission, CommissionCreditStatus
from sqlalchemy.sql import func

MAX_REFERRAL_UPLINE_LEVELS = 5

def get_commission_percentages_from_db(db: Session) -> Dict[int, decimal.Decimal]:
    levels_query = select(ReferralLevelPercentage.level, ReferralLevelPercentage.percentage)
    results = db.execute(levels_query).fetchall()
    default_rates = {
        1: decimal.Decimal("7.00"), 2: decimal.Decimal("5.00"),
        3: decimal.Decimal("4.00"), 4: decimal.Decimal("2.50"),
        5: decimal.Decimal("1.00"),
    }
    if not results:
        print("WARNING: ReferralLevelPercentage table is empty. Using default rates.")
        return {level: perc / decimal.Decimal("100.0") for level, perc in default_rates.items()}
    return {level: perc / decimal.Decimal("100.0") for level, perc in results}

def calculate_and_credit_referral_commissions(deposit_id: int, db: Session):
    """
    Calculates referral commissions for an approved deposit and attempts to credit them immediately.
    Logs the outcome of each commission attempt.
    """
    deposit = db.get(Deposit, deposit_id)
    # Assuming deposit.status is already confirmed 'approved' before calling this
    if not deposit: # Should not happen if called correctly
        print(f"CRITICAL: Deposit ID {deposit_id} not found in calculate_and_credit_referral_commissions.")
        return {"error": "Deposit not found during commission processing."}

    depositing_user_id = deposit.user_id
    deposit_amount = decimal.Decimal(str(deposit.amount)) # Convert Float to Decimal

    commission_rates = get_commission_percentages_from_db(db)
    if not commission_rates:
        print(f"ERROR: Commission rates missing for deposit ID {deposit_id}.")
        # Potentially log this as a system-level issue
        return {"error": "Commission rates configuration is missing."}

    cte_query_str = f"""
        WITH RECURSIVE referral_chain AS (
            SELECT id AS user_id, referrer_id, 1 AS current_depth
            FROM users WHERE id = :p_depositing_user_id
            UNION ALL
            SELECT u.id AS user_id, u.referrer_id, rc.current_depth + 1
            FROM users u INNER JOIN referral_chain rc ON u.id = rc.referrer_id
            WHERE rc.referrer_id IS NOT NULL AND rc.current_depth < :p_max_chain_depth
        )
        SELECT rc.user_id AS upline_recipient_id, (rc.current_depth - 1) AS commission_level_for_recipient
        FROM referral_chain rc
        WHERE rc.user_id != :p_depositing_user_id AND (rc.current_depth - 1) BETWEEN 1 AND :p_max_commission_upline_level;
    """
    params = {
        "p_depositing_user_id": depositing_user_id,
        "p_max_chain_depth": MAX_REFERRAL_UPLINE_LEVELS + 1,
        "p_max_commission_upline_level": MAX_REFERRAL_UPLINE_LEVELS
    }
    uplines_result = db.execute(text(cte_query_str), params).mappings().fetchall()

    processed_commissions_summary = []

    for row in uplines_result:
        upline_user_id = row["upline_recipient_id"]
        commission_level = int(row["commission_level_for_recipient"])
        
        commission_record = ReferralCommission(
            deposit_id=deposit.id,
            depositing_user_id=depositing_user_id,
            recipient_user_id=upline_user_id,
            commission_level=commission_level,
            status=CommissionCreditStatus.PENDING_PROCESSING # Initial status
            # commission_amount and percentage will be set below
        )

        percentage_decimal = commission_rates.get(commission_level)
        if percentage_decimal is None:
            print(f"Warning: No commission rate for level {commission_level} (User: {upline_user_id}, Deposit: {deposit_id}).")
            commission_record.commission_percentage_applied = decimal.Decimal("0.00")
            commission_record.commission_amount = decimal.Decimal("0.00")
            commission_record.status = CommissionCreditStatus.ERROR_CALCULATION
            # commission_record.processing_notes = "No commission rate defined for level."
            db.add(commission_record)
            processed_commissions_summary.append({
                "recipient_id": upline_user_id, "level": commission_level,
                "status": commission_record.status, "amount": 0
            })
            continue # Skip to next upline

        commission_record.commission_percentage_applied = percentage_decimal * 100
        calculated_amount = (deposit_amount * percentage_decimal).quantize(decimal.Decimal("0.01"), rounding=decimal.ROUND_DOWN)
        commission_record.commission_amount = calculated_amount

        if calculated_amount <= decimal.Decimal("0"):
            # commission_record.status = CommissionCreditStatus.SKIPPED_ZERO_AMOUNT # Optional status
            # commission_record.processing_notes = "Calculated commission is zero or less."
            # db.add(commission_record) # Decide if you want to log zero-amount commissions
            processed_commissions_summary.append({
                "recipient_id": upline_user_id, "level": commission_level,
                "status": "skipped_zero_amount", "amount": calculated_amount
            })
            continue # Skip actual crediting if amount is zero

        db.add(commission_record) # Add before attempting credit to ensure it's in session for ID

        # Attempt to credit the wallet immediately
        recipient_wallet = db.query(Wallet).filter(Wallet.user_id == upline_user_id).first()
        if not recipient_wallet:
            print(f"ERROR: Wallet not found for recipient user ID {upline_user_id} (Deposit: {deposit_id}).")
            commission_record.status = CommissionCreditStatus.ERROR_NO_WALLET
            # commission_record.processing_notes = "Recipient wallet not found."
            processed_commissions_summary.append({
                "recipient_id": upline_user_id, "level": commission_level,
                "status": commission_record.status, "amount": calculated_amount
            })
            continue # Skip to next upline, commission record will be saved with error status

        try:
            # Your Wallet model uses Float. Convert carefully.
            current_balance = decimal.Decimal(str(recipient_wallet.balance or 0.0))
            current_total_referral_bonus = decimal.Decimal(str(recipient_wallet.total_referral_bonus or 0.0))
            current_referral_bonus = decimal.Decimal(str(recipient_wallet.current_referral_bonus or 0.0))
            
            recipient_wallet.balance = float(current_balance + calculated_amount)
            recipient_wallet.total_referral_bonus = float(current_total_referral_bonus + calculated_amount)
            recipient_wallet.current_referral_bonus = float(current_referral_bonus + calculated_amount)
            # recipient_wallet.updated_at = func.now() # If BaseModel doesn't auto-update on change

            new_transaction_log = TransactionLedger(
                user_id=upline_user_id,
                amount=calculated_amount,
                transaction_type=TransactionType.REFERRAL_BONUS,
                reference_id=commission_record.id, # Will be available after flush/commit if not already
                description=f"L{commission_level} referral bonus. From Deposit ID: {deposit.id}."
                # created_at is handled by BaseModel or server_default
            )
            db.add(new_transaction_log)
            commission_record.status = CommissionCreditStatus.SUCCESS_CREDITED
            # commission_record.processing_notes = "Successfully credited."
            processed_commissions_summary.append({
                "recipient_id": upline_user_id, "level": commission_level,
                "status": commission_record.status, "amount": calculated_amount
            })
        except Exception as e_credit:
            print(f"ERROR crediting wallet for user {upline_user_id} (Commission for Deposit {deposit_id}): {e_credit}")
            # Don't rollback here, let the outer commit handle it or handle per commission
            commission_record.status = CommissionCreditStatus.ERROR_CREDITING_FAILED
            # commission_record.processing_notes = str(e_credit)
            processed_commissions_summary.append({
                "recipient_id": upline_user_id, "level": commission_level,
                "status": commission_record.status, "amount": calculated_amount, "error": str(e_credit)
            })
            # If one commission credit fails, we still want to try others and record all attempts.
            # The overall commit at the end will save all statuses.

    try:
        db.commit() # Commit all changes (new commissions, wallet updates, transaction logs)
    except Exception as e_commit:
        db.rollback()
        print(f"CRITICAL: Failed to commit referral commission processing for Deposit ID {deposit_id}: {e_commit}")
        # Here, all changes are rolled back. You might need a more sophisticated retry or error logging.
        # For simplicity, we're just rolling back if the final commit fails.
        # Individual error statuses on commission_records might not be saved if this overall commit fails.
        return {"error": "Failed to commit commission processing.", "details": str(e_commit), "summary": processed_commissions_summary}
        
    return {"message": "Referral commission processing complete.", "summary": processed_commissions_summary}


def admin_approve_deposit_and_process_commissions(deposit_id: int, deposit_status: DepositUpdate, db: Session, admin_user_id: int):
    """
    Admin approves a deposit, which then triggers the automated referral commission calculation and crediting.
    """
    # print("Starting admin approval process for deposit ID:", deposit_id)
    deposit_to_approve = db.get(Deposit, deposit_id)
    if not deposit_to_approve:
        return {"error": "Deposit not found."}

    # Assuming 'pending' is the status before approval. Adjust if needed.
    # if deposit_to_approve.status.lower() != "pending":
    #      return {"error": f"Deposit is not in 'pending' state. Current status: {deposit_to_approve.status}"}
    if deposit_status.status is not None:
        deposit_to_approve.status = deposit_status.status.lower() # Your 'approved' status string
        deposit_to_approve.reviewed_by_admin_id = admin_user_id # From your Deposit model
        deposit_to_approve.approved_at = func.now() # From your Deposit model
    
    try:
        db.commit() # Commit deposit approval first
        db.refresh(deposit_to_approve)
        print(f"Deposit ID {deposit_id} approved by admin {admin_user_id}.")
        
        # Now, process this approved deposit for referral commissions
        commission_results = calculate_and_credit_referral_commissions(deposit_id=deposit_id, db=db)
        return {
            "deposit_approval": "success",
            "deposit_id": deposit_id,
            "commission_processing_details": commission_results
        }
    except Exception as e:
        db.rollback()
        print(f"Error during deposit approval or subsequent commission processing for deposit {deposit_id}: {e}")
        return {"error": f"Failed to approve deposit or process commissions: {str(e)}"}