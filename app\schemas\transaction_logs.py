from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum





class TransactionLogBase(PydanticBaseModel):
    user_id: int
    type: str
    reference_id: Optional[int] = None
    amount: float
    direction: str
    remarks: Optional[str] = None

class TransactionLogCreate(TransactionLogBase):
    pass

class TransactionLogOut(TransactionLogBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True