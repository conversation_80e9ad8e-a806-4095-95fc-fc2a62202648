from datetime import datetime
from typing import Optional, List
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text, Enum as sqlEnum, Date
from enum import Enum
from app.models.deposits import Deposit


class RankEnum(str, Enum):
    bronze = "bronze"
    silver = "silver"
    gold = "gold"
    platinum = "platinum"

class StatusEnum(str, Enum):
    pending = "pending"
    verified = "verified"
    rejected = "rejected"

# USERS Table
class User(BaseModel):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    dob = Column(Date)
    nationality = Column(String(100), nullable=False)
    country_of_residence = Column(String(100), nullable=False)
    preferred_currency = Column(String(100), nullable=False)
    address = Column(Text, nullable=False)
    country_code = Column(String(10), nullable=False)
    phone = Column(String(15), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password = Column(Text, nullable=False)
    referral_code = Column(String(500), unique=True, index=True, nullable=False)
    referrer_id = Column(Integer, ForeignKey("users.id"))
    national_id = Column(Text, nullable=False)
    passport = Column(Text, nullable=False)
    rank = Column(sqlEnum(RankEnum), default=RankEnum.bronze) #platinum, gold, silver
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    is_kyc_verified = Column(Boolean, default=False)
    kyc_status = Column(sqlEnum(StatusEnum), default=StatusEnum.pending) #pending, verified, rejected
    profile_picture = Column(Text)
    
    # Relationships
    deposits = relationship("Deposit", back_populates="user", foreign_keys=[Deposit.user_id])
    wallet = relationship("Wallet", uselist=False, back_populates="user")
    profit_logs = relationship("ProfitLog", back_populates="user")
    withdrawals = relationship("Withdrawal", back_populates="user")
    # referral_commissions_given = relationship("ReferralCommission", foreign_keys="ReferralCommission.referrer_id", back_populates="referrer")
    # referral_commissions_received = relationship("ReferralCommission", foreign_keys="ReferralCommission.referred_user_id", back_populates="referred_user")
    transactions = relationship("TransactionLedger", back_populates="user")
    referral_commissions_given = relationship("ReferralCommission",foreign_keys="ReferralCommission.depositing_user_id",back_populates="original_depositor_info")
    referral_commissions_received = relationship("ReferralCommission",foreign_keys="ReferralCommission.recipient_user_id",back_populates="recipient")


