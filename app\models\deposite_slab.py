from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey



class DepositSlab(BaseModel):
    __tablename__ = "deposit_slabs"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    amount = Column(Float, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
