from datetime import date, datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum


class RankEnum(str, Enum):
    bronze = "bronze"
    silver = "silver"
    gold = "gold"
    platinum = "platinum"


class StatusEnum(str, Enum):
    pending = "pending"
    verified = "verified"
    rejected = "rejected"


class UserBase(PydanticBaseModel):
    name: str
    dob: Optional[date] = None
    nationality: str
    country_of_residence: str
    preferred_currency: str
    address: str
    country_code: str
    phone: str
    email: EmailStr
    national_id: str
    passport: str
    rank: RankEnum = RankEnum.bronze
    is_admin: bool = False
    is_active: bool = True
    is_kyc_verified: bool = False
    kyc_status: StatusEnum = StatusEnum.pending
    profile_picture: Optional[str] = None


class UserCreate(UserBase):
    referral_user_code: Optional[str] = None
    password: str 


class UserUpdate(PydanticBaseModel):
    name: Optional[str] = None
    dob: Optional[date] = None
    nationality: Optional[str] = None
    country_of_residence: Optional[str] = None
    preferred_currency: Optional[str] = None
    address: Optional[str] = None
    country_code: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    referral_code: Optional[str] = None
    referrer_id: Optional[int] = None
    national_id: Optional[str] = None
    passport: Optional[str] = None
    rank: Optional[RankEnum] = None
    is_admin: Optional[bool] = None
    is_active: Optional[bool] = None
    is_kyc_verified: Optional[bool] = None
    kyc_status: Optional[StatusEnum] = None
    profile_picture: Optional[str] = None



class UserOut(UserBase):  # Inherit UserBase but override if needed
    id: int
    referral_code: str
    referrer_id: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


class Login(PydanticBaseModel):
    email: EmailStr
    password: str
    

class KYCStatusUpdate(PydanticBaseModel):
    kyc_status: StatusEnum