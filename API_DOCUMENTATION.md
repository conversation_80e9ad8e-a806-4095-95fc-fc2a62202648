# Fruit-O-Kart Backend API Documentation

## Overview

This is a comprehensive FastAPI-based backend system for Fruit-O-Kart, a financial platform with features including user management, deposits, withdrawals, referral systems, and profit distribution.

**Generated on:** December 2024
**API Version:** 1.0
**Framework:** FastAPI with SQLAlchemy ORM

## Base URL
```
http://localhost:8000
```

## Authentication

The API uses JWT (JSON Web Token) based authentication. Most endpoints require authentication via Bear<PERSON> token.

### Authentication Headers
```
Authorization: Bearer <access_token>
```

### Login Endpoint
```http
POST /users/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### Refresh Token Endpoint
```http
POST /users/refresh
```

**Request Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Pagination

All list endpoints support pagination with the following query parameters:

- `skip`: Number of items to skip (default: 0, min: 0)
- `limit`: Number of items to return (default: 10, min: 1, max: 100)

**Pagination Response Format:**
```json
{
  "total": 100,
  "skip": 0,
  "limit": 10,
  "has_next": true,
  "has_prev": false,
  "items": [...]
}
```

## API Endpoints

### 1. Users Management

#### Get All Users
```http
GET /users?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of users

#### Get User by ID
```http
GET /users/{user_id}
```
**Authentication:** Required
**Response:** User details

#### Create User
```http
POST /users
```
**Authentication:** Not required
**Request Body:**
```json
{
  "name": "John Doe",
  "dob": "1990-01-01",
  "nationality": "US",
  "country_of_residence": "US",
  "preferred_currency": "USD",
  "address": "123 Main St",
  "country_code": "+1",
  "phone": "*********0",
  "email": "<EMAIL>",
  "password": "password123",
  "referral_code": "REF123",
  "referrer_id": 1,
  "national_id": "*********",
  "passport": "P*********"
}
```

#### Update User
```http
PUT /users/{user_id}
```
**Authentication:** Required
**Request Body:** Partial user data (all fields optional)

#### Delete User
```http
DELETE /users/{user_id}
```
**Authentication:** Required
**Response:** Success message

### 2. Products Management

#### Get All Products
```http
GET /products?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of products

#### Get Product by ID
```http
GET /products/{product_id}
```
**Authentication:** Required
**Response:** Product details

#### Create Product
```http
POST /products
```
**Authentication:** Required
**Request Body:**
```json
{
  "name": "Apple",
  "description": "Fresh red apples",
  "image_url": "https://example.com/apple.jpg",
  "active": true
}
```

#### Update Product
```http
PUT /products/{product_id}
```
**Authentication:** Required
**Request Body:** Partial product data

#### Delete Product
```http
DELETE /products/{product_id}
```
**Authentication:** Required
**Response:** Success message

### 3. Deposits Management

#### Get All Deposits
```http
GET /deposits?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of deposits

#### Get Deposit by ID
```http
GET /deposits/{deposit_id}
```
**Authentication:** Required
**Response:** Deposit details

#### Create Deposit
```http
POST /deposits
```
**Authentication:** Required
**Request Body:**
```json
{
  "admin_wallet_id": "wallet123",
  "amount": 1000.00,
  "transaction_id": "txn123456"
}
```

#### Approve Deposit (Admin Only)
```http
POST /deposits/{deposit_id}
```
**Authentication:** Admin required
**Response:** Updated deposit with approval details

#### Delete Deposit
```http
DELETE /deposits/{deposit_id}
```
**Authentication:** Required
**Response:** Success message

### 4. Withdrawals Management

#### Get All Withdrawals
```http
GET /withdrawals?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of withdrawals

#### Get Withdrawal by ID
```http
GET /withdrawals/{withdrawal_id}
```
**Authentication:** Required
**Response:** Withdrawal details

#### Create Withdrawal
```http
POST /withdrawals
```
**Authentication:** Required
**Request Body:**
```json
{
  "wallet_address": "0x*********0abcdef",
  "amount": 500.00,
  "network": "ETH"
}
```

#### Update Withdrawal
```http
PUT /withdrawals/{withdrawal_id}
```
**Authentication:** Required
**Request Body:**
```json
{
  "status": "approved",
  "approved_at": "2024-01-01T12:00:00Z"
}
```

#### Delete Withdrawal
```http
DELETE /withdrawals/{withdrawal_id}
```
**Authentication:** Required
**Response:** Success message

### 5. Wallets Management

#### Get All Wallets
```http
GET /wallets?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of wallets

#### Get Wallet by ID
```http
GET /wallets/{wallet_id}
```
**Authentication:** Required
**Response:** Wallet details

#### Create Wallet
```http
POST /wallets
```
**Authentication:** Required
**Request Body:**
```json
{
  "user_id": 1,
  "balance": 0.0,
  "total_profit": 0.0,
  "total_referral_bonus": 0.0
}
```

#### Update Wallet
```http
PUT /wallets/{wallet_id}
```
**Authentication:** Required
**Request Body:** Partial wallet data

#### Delete Wallet
```http
DELETE /wallets/{wallet_id}
```
**Authentication:** Required
**Response:** Success message

### 6. Admin Wallets Management

#### Get All Admin Wallets
```http
GET /admin_wallets?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of admin wallets

#### Get Admin Wallet by ID
```http
GET /admin_wallets/{admin_wallet_id}
```
**Authentication:** Required
**Response:** Admin wallet details

#### Create Admin Wallet
```http
POST /admin_wallets
```
**Authentication:** Required
**Request Body:**
```json
{
  "wallet_address": "0x*********0abcdef",
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "network": "ETH",
  "is_active": true
}
```

#### Update Admin Wallet
```http
PUT /admin_wallets/{admin_wallet_id}
```
**Authentication:** Required
**Request Body:** Partial admin wallet data

#### Delete Admin Wallet
```http
DELETE /admin_wallets/{admin_wallet_id}
```
**Authentication:** Required
**Response:** Success message

### 7. Admin Profits Management

#### Get All Admin Profits
```http
GET /admin_profits?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of admin profits

#### Get Admin Profit by ID
```http
GET /admin_profits/{admin_profit_id}
```
**Authentication:** Required
**Response:** Admin profit details

#### Create Admin Profit
```http
POST /admin_profits
```
**Authentication:** Required
**Request Body:**
```json
{
  "today_rate": 2.5
}
```

#### Update Admin Profit
```http
PUT /admin_profits/{admin_profit_id}
```
**Authentication:** Required
**Request Body:**
```json
{
  "today_rate": 3.0
}
```

#### Delete Admin Profit
```http
DELETE /admin_profits/{admin_profit_id}
```
**Authentication:** Required
**Response:** Success message

### 8. Currency Conversions Management

#### Get All Currency Conversions
```http
GET /currency_conversions?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of currency conversions

#### Get Currency Conversion by ID
```http
GET /currency_conversions/{currency_conversion_id}
```
**Authentication:** Required
**Response:** Currency conversion details

#### Create Currency Conversion
```http
POST /currency_conversions
```
**Authentication:** Required
**Request Body:**
```json
{
  "from_currency": "USD",
  "code": "EUR",
  "conversion_rate": 0.85,
  "is_active": true
}
```

#### Update Currency Conversion
```http
PUT /currency_conversions/{currency_conversion_id}
```
**Authentication:** Required
**Request Body:** Partial currency conversion data

#### Delete Currency Conversion
```http
DELETE /currency_conversions/{currency_conversion_id}
```
**Authentication:** Required
**Response:** Success message

### 9. Deposit Slabs Management

#### Get All Deposit Slabs
```http
GET /deposite_slabs?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of deposit slabs

#### Get Deposit Slab by ID
```http
GET /deposite_slabs/{deposite_slab_id}
```
**Authentication:** Required
**Response:** Deposit slab details

#### Create Deposit Slab
```http
POST /deposite_slabs
```
**Authentication:** Required
**Request Body:**
```json
{
  "name": "Bronze Package",
  "amount": 1000.00,
  "is_active": true
}
```

#### Update Deposit Slab
```http
PUT /deposite_slabs/{deposite_slab_id}
```
**Authentication:** Required
**Request Body:** Partial deposit slab data

#### Delete Deposit Slab
```http
DELETE /deposite_slabs/{deposite_slab_id}
```
**Authentication:** Required
**Response:** Success message

### 10. Privacy & Policy Management

#### Get All Privacy Policies
```http
GET /privacy_and_policys?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of privacy policies

#### Get Privacy Policy by ID
```http
GET /privacy_and_policys/{privacy_and_policy_id}
```
**Authentication:** Required
**Response:** Privacy policy details

#### Create Privacy Policy
```http
POST /privacy_and_policys
```
**Authentication:** Required
**Request Body:**
```json
{
  "title": "Privacy Policy",
  "description": "This is our privacy policy...",
  "is_active": true
}
```

#### Update Privacy Policy
```http
PUT /privacy_and_policys/{privacy_and_policy_id}
```
**Authentication:** Required
**Request Body:** Partial privacy policy data

#### Delete Privacy Policy
```http
DELETE /privacy_and_policys/{privacy_and_policy_id}
```
**Authentication:** Required
**Response:** Success message

### 11. Terms & Conditions Management

#### Get All Terms & Conditions
```http
GET /terms_and_conditions?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of terms & conditions

#### Get Terms & Conditions by ID
```http
GET /terms_and_conditions/{terms_and_condition_id}
```
**Authentication:** Required
**Response:** Terms & conditions details

#### Create Terms & Conditions
```http
POST /terms_and_conditions
```
**Authentication:** Required
**Request Body:**
```json
{
  "title": "Terms and Conditions",
  "description": "These are our terms and conditions...",
  "is_active": true
}
```

#### Update Terms & Conditions
```http
PUT /terms_and_conditions/{terms_and_condition_id}
```
**Authentication:** Required
**Request Body:** Partial terms & conditions data

#### Delete Terms & Conditions
```http
DELETE /terms_and_conditions/{terms_and_condition_id}
```
**Authentication:** Required
**Response:** Success message

### 12. Transaction Logs Management

#### Get All Transaction Logs
```http
GET /transaction_logs?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of transaction logs

#### Get Transaction Log by ID
```http
GET /transaction_logs/{transaction_log_id}
```
**Authentication:** Required
**Response:** Transaction log details

#### Create Transaction Log
```http
POST /transaction_logs
```
**Authentication:** Required
**Request Body:**
```json
{
  "user_id": 1,
  "type": "deposit",
  "reference_id": 123,
  "amount": 1000.00,
  "direction": "credit",
  "remarks": "Deposit approved"
}
```

#### Delete Transaction Log
```http
DELETE /transaction_logs/{transaction_log_id}
```
**Authentication:** Required
**Response:** Success message

### 13. Profit Logs Management

#### Get All Profit Logs
```http
GET /profit_logs?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of profit logs

#### Get Profit Log by ID
```http
GET /profit_logs/{profit_log_id}
```
**Authentication:** Required
**Response:** Profit log details

#### Create Profit Log
```http
POST /profit_logs
```
**Authentication:** Required
**Request Body:**
```json
{
  "deposit_id": 1,
  "user_id": 1,
  "profit_amount": 50.00,
  "profit_percent": 5.0
}
```

#### Delete Profit Log
```http
DELETE /profit_logs/{profit_log_id}
```
**Authentication:** Required
**Response:** Success message

### 14. Referral Level Percentages Management

#### Get All Referral Level Percentages
```http
GET /referral_level_percentages?skip=0&limit=10
```
**Authentication:** Required
**Response:** Paginated list of referral level percentages

#### Get Referral Level Percentage by ID
```http
GET /referral_level_percentages/{referral_level_percentage_id}
```
**Authentication:** Required
**Response:** Referral level percentage details

#### Create Referral Level Percentage
```http
POST /referral_level_percentages
```
**Authentication:** Required
**Request Body:**
```json
{
  "level": 1,
  "percentage": 10.5,
  "description": "First level referral bonus"
}
```

#### Update Referral Level Percentage
```http
PUT /referral_level_percentages/{referral_level_percentage_id}
```
**Authentication:** Required
**Request Body:**
```json
{
  "percentage": 12.0,
  "description": "Updated first level referral bonus"
}
```

#### Delete Referral Level Percentage
```http
DELETE /referral_level_percentages/{referral_level_percentage_id}
```
**Authentication:** Required
**Response:** Success message

## Data Schemas

### User Schema

**UserOut Response:**
```json
{
  "id": 1,
  "name": "John Doe",
  "dob": "1990-01-01",
  "nationality": "US",
  "country_of_residence": "US",
  "preferred_currency": "USD",
  "address": "123 Main St",
  "country_code": "+1",
  "phone": "*********0",
  "email": "<EMAIL>",
  "referral_code": "REF123",
  "referrer_id": 1,
  "national_id": "*********",
  "passport": "P*********",
  "rank": "bronze",
  "is_admin": false,
  "is_active": true,
  "is_kyc_verified": false,
  "kyc_status": "pending",
  "profile_picture": null,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

**User Enums:**
- `rank`: "bronze", "silver", "gold", "platinum"
- `kyc_status`: "pending", "verified", "rejected"

### Deposit Schema

**DepositOut Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "admin_wallet_id": "wallet123",
  "amount": 1000.00,
  "transaction_id": "txn123456",
  "status": "pending",
  "reviewed_by_admin_id": null,
  "approved_at": null,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### Withdrawal Schema

**WithdrawalOut Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "wallet_address": "0x*********0abcdef",
  "amount": 500.00,
  "network": "ETH",
  "status": "pending",
  "approved_at": null,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Wallet Schema

**WalletOut Response:**
```json
{
  "user_id": 1,
  "balance": 1500.00,
  "total_profit": 150.00,
  "total_referral_bonus": 50.00
}
```

### Product Schema

**ProductOut Response:**
```json
{
  "id": 1,
  "name": "Apple",
  "description": "Fresh red apples",
  "image_url": "https://example.com/apple.jpg",
  "active": true,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Admin Wallet Schema

**AdminWalletOut Response:**
```json
{
  "id": 1,
  "wallet_address": "0x*********0abcdef",
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "network": "ETH",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Admin Profit Schema

**AdminProfitOut Response:**
```json
{
  "id": 1,
  "today_rate": 2.5,
  "total_user_getting_profit": 100,
  "total_net_deposit": "50000.00",
  "distributed_amount": "1250.00",
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Currency Conversion Schema

**CurrencyConversionOut Response:**
```json
{
  "id": 1,
  "from_currency": "USD",
  "code": "EUR",
  "conversion_rate": 0.85,
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Deposit Slab Schema

**DepositSlabOut Response:**
```json
{
  "id": 1,
  "name": "Bronze Package",
  "amount": 1000.00,
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Transaction Log Schema

**TransactionLogOut Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "type": "deposit",
  "reference_id": 123,
  "amount": 1000.00,
  "direction": "credit",
  "remarks": "Deposit approved",
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Profit Log Schema

**ProfitLogOut Response:**
```json
{
  "id": 1,
  "deposit_id": 1,
  "user_id": 1,
  "profit_amount": 50.00,
  "profit_percent": 5.0,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Referral Level Percentage Schema

**ReferralLevelPercentageOut Response:**
```json
{
  "level": 1,
  "percentage": 10.5,
  "description": "First level referral bonus"
}
```

## Error Handling

### Standard Error Response Format

```json
{
  "detail": "Error message description"
}
```

### Common HTTP Status Codes

- **200 OK**: Successful GET, PUT, DELETE operations
- **201 Created**: Successful POST operations
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required or invalid token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

### Authentication Errors

**401 Unauthorized:**
```json
{
  "detail": "Invalid credentials"
}
```

**401 Unauthorized (Token):**
```json
{
  "detail": "Invalid refresh token"
}
```

### Validation Errors

**422 Unprocessable Entity:**
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### Resource Not Found

**404 Not Found:**
```json
{
  "detail": "user not found"
}
```

## Notes

1. **Authentication**: Most endpoints require authentication via Bearer token. Admin-only endpoints require admin privileges.

2. **Pagination**: All list endpoints support pagination with `skip` and `limit` parameters.

3. **Timestamps**: All timestamps are in ISO 8601 format (UTC).

4. **Decimal Fields**: Financial amounts use decimal precision for accuracy.

5. **Status Fields**: Many entities have status fields that track their current state (pending, approved, rejected, etc.).

6. **Soft Deletes**: Some entities may use soft deletes (is_deleted flag) instead of hard deletes.

7. **Referral System**: The platform includes a multi-level referral system with configurable percentages.

8. **KYC Verification**: Users have KYC status tracking for compliance purposes.

This documentation covers all the main API endpoints and data structures for the Fruit-O-Kart backend system. For additional details or specific implementation questions, refer to the source code or contact the development team.
