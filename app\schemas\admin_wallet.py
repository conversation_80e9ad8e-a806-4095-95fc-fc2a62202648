from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class AdminWalletBase(PydanticBaseModel):
    wallet_address: str
    qr_code: str
    network: str
    is_active: bool = True


class AdminWalletCreate(AdminWalletBase):
    pass


class AdminWalletUpdate(PydanticBaseModel):
    wallet_address: Optional[str] = None
    qr_code: Optional[str] = None
    network: Optional[str] = None
    is_active: Optional[bool] = None


class AdminWalletOut(AdminWalletBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
