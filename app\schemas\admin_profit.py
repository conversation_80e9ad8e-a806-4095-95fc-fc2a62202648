from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class AdminProfitBase(PydanticBaseModel):
    today_rate: float
    total_user_getting_profit: int
    total_net_deposit: str
    distributed_amount: str
    # You can also add `active: bool = True` if needed

class AdminProfitCreate(PydanticBaseModel):
    today_rate: float

class AdminProfitUpdate(PydanticBaseModel):
    today_rate: Optional[float] = None

class AdminProfitOut(AdminProfitBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
