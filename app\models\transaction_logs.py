from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, Numeric, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.sql import func
import enum


class TransactionType(str, enum.Enum):
    DEPOSIT_WALLET_CREDIT = "deposit_wallet_credit"
    REFERRAL_BONUS = "referral_bonus"
    WITHDRAWAL = "withdrawal"
    DAILY_PROFIT = "daily_profit"
    

class TransactionLedger(BaseModel):
    __tablename__ = "transaction_ledger"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)
    transaction_type = Column(SQLEnum(TransactionType), nullable=False)
    reference_id = Column(Integer, nullable=True, index=True)
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    user = relationship("User", back_populates="transactions")
