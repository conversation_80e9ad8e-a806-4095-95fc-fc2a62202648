# # In a new file, e.g., app/initial_data.py or app/db/init_db.py
# from sqlalchemy.orm import Session
# from app.models.referral_percentage import ReferralLevelPercentage # Adjust import path
# # Import your SessionLocal or however you create a DB session for scripts/startup
# from app.config.db import SessionLocal # Example: if you have SessionLocal defined here

# # Or, if your main.py creates the engine and SessionLocal, you might pass SessionLocal
# # or a dependency to get the DB session. For startup events, creating a local session is common.

# REFERRAL_LEVELS_DATA = [
#     {"level": 1, "percentage": 7.00, "description": "Direct referral bonus"},
#     {"level": 2, "percentage": 5.00, "description": "Second-tier referral bonus"},
#     {"level": 3, "percentage": 4.00, "description": "Third-tier referral bonus"},
#     {"level": 4, "percentage": 2.50, "description": "Fourth-tier referral bonus"},
#     {"level": 5, "percentage": 1.00, "description": "Fifth-tier referral bonus"},
# ]

# def create_initial_referral_levels(db: Session):
#     print("Attempting to seed initial referral level percentages...")
#     try:
#         existing_levels_query = db.query(ReferralLevelPercentage.level)
#         existing_levels = {level_row[0] for level_row in existing_levels_query.all()}
        
#         levels_to_add = []
#         for level_data in REFERRAL_LEVELS_DATA:
#             if level_data["level"] not in existing_levels:
#                 new_level = ReferralLevelPercentage(
#                     level=level_data["level"],
#                     percentage=level_data["percentage"],
#                     description=level_data["description"]
#                     # created_at and updated_at might be handled by your BaseModel
#                 )
#                 levels_to_add.append(new_level)
#                 print(f"  Queueing Level {level_data['level']} with {level_data['percentage']}% for creation.")
#             else:
#                 # print(f"  Level {level_data['level']} already exists. Skipping.")
#                 pass

#         if levels_to_add:
#             db.add_all(levels_to_add)
#             db.commit()
#             for level in levels_to_add:
#                 db.refresh(level) # To get any DB-generated values like auto-increment IDs if not level itself
#             print(f"Successfully added {len(levels_to_add)} new referral level percentages.")
#         else:
#             print("All referral level percentages already exist. No new levels added.")

#     except Exception as e:
#         print(f"Error during initial referral level seeding: {e}")
#         db.rollback() # Rollback in case of any error during the process
#     finally:
#         # The session passed to this function should be managed by the caller in the startup event
#         pass

# # If you need to call this with a self-managed session (e.g., for testing this function):
# # def init_db_main():
# #     db = SessionLocal()
# #     try:
# #         create_initial_referral_levels(db)
# #     finally:
# #         db.close()
# #
# # if __name__ == "__main__":
# #     init_db_main()