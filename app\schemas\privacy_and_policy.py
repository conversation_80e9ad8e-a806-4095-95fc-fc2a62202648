from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class PrivacyAndPolicyBase(PydanticBaseModel):
    title: str
    description: str
    is_active: bool = True


class PrivacyAndPolicyCreate(PrivacyAndPolicyBase):
    pass


class PrivacyAndPolicyUpdate(PydanticBaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class PrivacyAndPolicyOut(PrivacyAndPolicyBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
