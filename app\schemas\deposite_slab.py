from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class DepositSlabBase(PydanticBaseModel):
    name: str
    amount: float
    is_active: bool = True


class DepositSlabCreate(DepositSlabBase):
    pass


class DepositSlabUpdate(PydanticBaseModel):
    name: Optional[str] = None
    amount: Optional[float] = None
    is_active: Optional[bool] = None


class DepositSlabOut(DepositSlabBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
