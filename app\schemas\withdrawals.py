from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum





class WithdrawalBase(PydanticBaseModel):
    user_id: int
    wallet_address: str
    amount: float
    network: str
    status: Optional[str] = None
    approved_at: Optional[datetime] = None

class WithdrawalCreate(PydanticBaseModel):
    wallet_address: str
    amount: float
    network: str

class WithdrawalUpdate(PydanticBaseModel):
    status: Optional[str] = None
    approved_at: Optional[datetime] = None

class WithdrawalOut(WithdrawalBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True