from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class TermsAndConditionsBase(PydanticBaseModel):
    title: str
    description: str
    is_active: bool = True


class TermsAndConditionsCreate(TermsAndConditionsBase):
    pass


class TermsAndConditionsUpdate(PydanticBaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class TermsAndConditionsOut(TermsAndConditionsBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
