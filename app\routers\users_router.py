from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.users import UserCreate, UserUpdate, UserOut, Login
from app.services.users_service import get_users, get_user, create_user, update_user, delete_user
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from fastapi.security import OAuth2PasswordRequestForm
from app.models.users import User
from app.utils.auth_utility import create_access_token, verify_password, create_refresh_token, decode_access_token


router = APIRouter(
    prefix="/users",
    tags=["users"]
)

@router.post("/login")
def login(login_data: Login, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == login_data.email).first()
    if not user or not verify_password(login_data.password, user.password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    access_token = create_access_token(data={"sub": user.email})
    refresh_token = create_refresh_token(data={"sub": user.email})
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }
    

@router.post("/refresh")
def refresh_token(refresh_token: str):
    payload = decode_access_token(refresh_token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token")
    
    # Optionally validate against stored refresh token in DB (recommended in production)
    new_access_token = create_access_token(data={"sub": payload["sub"]})
    return {"access_token": new_access_token, "token_type": "bearer"}



@router.get("/", response_model=Page[UserOut])
def read_users(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db)
):
    return get_users(db, params=params)

@router.get("/{user_id}", response_model=UserOut)
def read_user(user_id: int, db: Session = Depends(get_db)):
    user = get_user(db=db, User_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return user

@router.post("/", response_model=UserOut, status_code=status.HTTP_201_CREATED)
def create_new_user(user: UserCreate, db: Session = Depends(get_db)):
    return create_user(db=db, user=user)

@router.put("/{user_id}", response_model=UserOut)
def update_existing_user(user_id: int, user: UserUpdate, db: Session = Depends(get_db)):
    updated_user = update_user(db=db, User_id=user_id, User=user)
    if updated_user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return updated_user

@router.delete("/{user_id}", status_code=status.HTTP_200_OK)
def delete_existing_user(user_id: int, db: Session = Depends(get_db)):
    success = delete_user(db=db, User_id=user_id)
    if not success:
        raise HTTPException(status_code=404, detail="user not found")
    return {"detail": "user deleted successfully"}