"""admin wallet id in deposit model changed from fk to string

Revision ID: 04e586413b09
Revises: 06efa559bf0a
Create Date: 2025-05-23 17:23:11.890504

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '04e586413b09'
down_revision: Union[str, None] = '06efa559bf0a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('deposits_ibfk_1', 'deposits', type_='foreignkey')
    op.alter_column('deposits', 'admin_wallet_id',
               existing_type=mysql.INTEGER(),
               type_=sa.String(length=200),
               existing_nullable=False)
    op.create_index(op.f('ix_deposits_admin_wallet_id'), 'deposits', ['admin_wallet_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('deposits_ibfk_1', 'deposits', 'admin_wallets', ['admin_wallet_id'], ['id'])
    op.drop_index(op.f('ix_deposits_admin_wallet_id'), table_name='deposits')
    op.alter_column('deposits', 'admin_wallet_id',
               existing_type=sa.String(length=200),
               type_=mysql.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
