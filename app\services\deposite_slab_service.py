from sqlalchemy.orm import Session
from app.models.deposite_slab import DepositSlab
from app.schemas.deposite_slab import DepositSlabCreate, DepositSlabUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, <PERSON>

def get_deposit_slabs(db: Session, params: PaginationParams) -> Page[DepositSlab]:
    query = db.query(DepositSlab)
    return paginate(query, params)

def get_deposit_slab(db: Session, DepositSlab_id: int) -> Optional[DepositSlab]:
    return db.query(DepositSlab).filter(DepositSlab.id == DepositSlab_id).first()

def create_deposit_slab(db: Session, DepositSlab: DepositSlabCreate) -> DepositSlab:
    db_DepositSlab = DepositSlab(**DepositSlab.dict())
    db.add(db_DepositSlab)
    db.commit()
    db.refresh(db_DepositSlab)
    return db_DepositSlab

def update_deposit_slab(db: Session, DepositSlab_id: int, DepositSlab: DepositSlabUpdate) -> Optional[DepositSlab]:
    db_DepositSlab = get_deposit_slab(db, DepositSlab_id)
    if db_DepositSlab:
        update_data = DepositSlab.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_DepositSlab, key, value)
        db.commit()
        db.refresh(db_DepositSlab)
    return db_DepositSlab

def delete_deposit_slab(db: Session, DepositSlab_id: int) -> bool:
    db_DepositSlab = get_deposit_slab(db, DepositSlab_id)
    if db_DepositSlab:
        db.delete(db_DepositSlab)
        db.commit()
        return True
    return False