from sqlalchemy.orm import Session
from app.models.users import User
from app.schemas.users import UserCreate, UserUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.utils.auth_utility import hash_password, generate_referral_code
from app.services.wallet_service import create_wallet



def get_users(db: Session, params: PaginationParams) -> Page[User]:
    query = db.query(User)
    return paginate(query, params)

def get_user(db: Session, User_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == User_id).first()

def create_user(db: Session, user: UserCreate) -> User:
    # this is to get the reffered user's id and store it in referrer_id
    referral_user_id = None
    if user.referral_user_code is not None:
        user_referral_code = db.query(User).filter(User.referral_code == user.referral_user_code).first()
        referral_user_id = user_referral_code.id if user_referral_code else None
    
    #this is to generate a unique referral code for the new user
    generated_referral_code = generate_referral_code()
    while db.query(User).filter(User.referral_code == generated_referral_code).first():
        generated_referral_code = generate_referral_code()

    user_data = user.dict(exclude={"password", "referral_code", "referral_user_code", "referrer_id"})

    db_User = User(
        **user_data,
        password=hash_password(user.password),
        referral_code=generated_referral_code,
        referrer_id=referral_user_id
    )
    db.add(db_User)
    db.commit()
    db.refresh(db_User)
    if db_User.is_admin is False:
        new_wallet = create_wallet(db, user_id=db_User.id, balance=0, total_referral_bonus=0, total_profit=0)
    return db_User

def update_user(db: Session, User_id: int, User: UserUpdate) -> Optional[User]:
    db_User = get_user(db, User_id)
    if db_User:
        update_data = User.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_User, key, value)
        db.commit()
        db.refresh(db_User)
    return db_User

def delete_user(db: Session, User_id: int) -> bool:
    db_User = get_user(db, User_id)
    if db_User:
        db_User.is_active = False
        db.commit()
        return True
    return False