from sqlalchemy.orm import Session
from app.models.users import User
from app.schemas.users import UserCreate, UserUpdate, KYCStatusUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.utils.auth_utility import hash_password, generate_referral_code
from app.services.wallet_service import create_wallet
from sqlalchemy import text
from app.utils.referral_trees import build_referral_tree


def get_users(db: Session, params: PaginationParams) -> Page[User]:
    query = db.query(User)
    return paginate(query, params)

def get_user(db: Session, User_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == User_id).first()

def create_user(db: Session, user: UserCreate) -> User:
    # this is to get the reffered user's id and store it in referrer_id
    referral_user_id = None
    if user.referral_user_code is not None:
        user_referral_code = db.query(User).filter(User.referral_code == user.referral_user_code).first()
        referral_user_id = user_referral_code.id if user_referral_code else None
    
    #this is to generate a unique referral code for the new user
    generated_referral_code = generate_referral_code()
    while db.query(User).filter(User.referral_code == generated_referral_code).first():
        generated_referral_code = generate_referral_code()

    user_data = user.dict(exclude={"password", "referral_code", "referral_user_code", "referrer_id"})

    db_User = User(
        **user_data,
        password=hash_password(user.password),
        referral_code=generated_referral_code,
        referrer_id=referral_user_id
    )
    db.add(db_User)
    db.commit()
    db.refresh(db_User)
    if db_User.is_admin is False:
        new_wallet = create_wallet(db, user_id=db_User.id, balance=0, total_referral_bonus=0, total_profit=0)
    return db_User

def update_user(db: Session, User_id: int, User: UserUpdate) -> Optional[User]:
    db_User = get_user(db, User_id)
    if db_User:
        update_data = User.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_User, key, value)
        db.commit()
        db.refresh(db_User)
    return db_User

def delete_user(db: Session, User_id: int) -> bool:
    db_User = get_user(db, User_id)
    if db_User:
        db_User.is_active = False
        db.commit()
        return True
    return False


def get_user_referrals(db: Session, user_id: int) -> Optional[User]:
    
    main_user = get_user(db, user_id)
    if main_user:
        # direct_leg_users = db.query(User).filter(User.referrer_id == main_user.id).all()
        sql = text("""
        WITH RECURSIVE referral_tree AS (
            SELECT id AS user_id, referrer_id, 0 AS level
            FROM users
            WHERE id = :root_user_id
            UNION ALL
            SELECT u.id, u.referrer_id, rt.level + 1
            FROM users u
            INNER JOIN referral_tree rt ON u.referrer_id = rt.user_id
        ),
        user_deposits AS (
            SELECT user_id, SUM(amount) AS total_deposit
            FROM deposits
            WHERE status = 'approved'
            GROUP BY user_id
        ),
        user_commissions AS (
            SELECT depositing_user_id AS user_id, SUM(commission_amount) AS commission_to_root
            FROM referral_commissions
            WHERE recipient_user_id = :root_user_id
            GROUP BY depositing_user_id
        )
        SELECT 
            rt.user_id,
            rt.referrer_id,
            rt.level,
            u.name,
            COALESCE(ud.total_deposit, 0) AS total_deposit,
            COALESCE(uc.commission_to_root, 0) AS commission_to_root
        FROM referral_tree rt
        JOIN users u ON u.id = rt.user_id
        LEFT JOIN user_deposits ud ON ud.user_id = rt.user_id
        LEFT JOIN user_commissions uc ON uc.user_id = rt.user_id
        ORDER BY rt.level, rt.user_id;

        """)


        result = db.execute(sql, {"root_user_id": user_id}).mappings().all()
        referral_tree = build_referral_tree(user_id, result)
        return referral_tree
    return {"detail":"user not found"}


def update_kyc_status(db: Session, user_id: int, kyc_status: KYCStatusUpdate):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db_user.kyc_status = kyc_status.kyc_status
    if kyc_status.kyc_status.lower() == "verified":
        db_user.is_kyc_verified = True
    elif kyc_status.kyc_status.lower() == "rejected" or "pending":
        db_user.is_kyc_verified = False
    db.commit()
    db.refresh(db_user)
    return db_user