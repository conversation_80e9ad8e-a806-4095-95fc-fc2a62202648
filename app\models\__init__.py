from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


from .admin_profit import AdminProfit
from .admin_wallet import AdminWallet
from .base import BaseModel
from .currency_convertion import CurrencyConversion
from .deposite_slab import DepositSlab
from .deposits import Deposit
from .privacy_and_policy import PrivacyAndPolicy
from .products import Product
from .profit_logs import ProfitLog
from .terms_and_conditions import TermsAndConditions
from .transaction_logs import TransactionLedger
from .users import User
from .referral_commision import ReferralCommission
from .referral_percentage import ReferralLevelPercentage
from .wallets import Wallet
from .withdrawals import Withdrawal