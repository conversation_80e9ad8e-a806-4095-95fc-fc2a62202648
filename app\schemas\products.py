from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum





class ProductBase(PydanticBaseModel):
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    active: bool = True

class ProductCreate(ProductBase):
    pass

class ProductUpdate(PydanticBaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    active: Optional[bool] = None

class ProductOut(ProductBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True