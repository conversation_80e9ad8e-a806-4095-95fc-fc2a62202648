from sqlalchemy.orm import Session
from app.models.admin_profit import AdminProfit
from app.schemas.admin_profit import AdminProfitCreate, AdminProfitUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.models.users import User
from app.models.deposits import Deposit
from sqlalchemy import func
from app.services.wallet_service import update_wallet
from app.models.wallets import Wallet


def get_admin_profits(db: Session, params: PaginationParams) -> Page[AdminProfit]:
    query = db.query(AdminProfit)
    return paginate(query, params)

def get_admin_profit(db: Session, admin_profit_id: int) -> Optional[AdminProfit]:
    return db.query(AdminProfit).filter(AdminProfit.id == admin_profit_id).first()

def create_admin_profit(db: Session, AdminProfit_data: AdminProfitCreate) -> AdminProfit:
    # total_user_profit = db.query(User).filter(User.is_active == True).count()
    # total_deposit = db.query(func.sum(Deposit.amount)).filter(Deposit.status == "approved").scalar()
    # profit_rate = AdminProfit_data.today_rate / 100
    # total_distributed = db.query(func.sum(Deposit.amount * profit_rate)).filter(Deposit.status == "approved").scalar() or 0
    # db_admin_profit = AdminProfit(today_rate=AdminProfit_data.today_rate,
    #                               total_user_getting_profit=total_user_profit,
    #                               total_net_deposit=total_deposit,
    #                               distributed_amount=total_distributed)
    # db.add(db_admin_profit)
    # db.commit()
    # db.refresh(db_admin_profit)
    # wallet_amount_update = update_wallet(db=db, wallet_id=)
    # return db_admin_profit
    # 1. Get total active users
    total_user_profit = db.query(User).filter(User.is_active == True).count()

    # 2. Get total approved deposits
    total_deposit = db.query(func.sum(Deposit.amount)).filter(Deposit.status == "approved").scalar() or 0

    # 3. Calculate profit rate
    profit_rate = AdminProfit_data.today_rate / 100

    # 4. Get total profit to distribute (just for logging)
    total_distributed = total_deposit * profit_rate

    # 5. Create the admin profit record
    db_admin_profit = AdminProfit(
        today_rate=AdminProfit_data.today_rate,
        total_user_getting_profit=total_user_profit,
        total_net_deposit=total_deposit,
        distributed_amount=total_distributed
    )
    db.add(db_admin_profit)

    # 6. Get each user's total approved deposit
    deposit_sums_query = db.query(
        Deposit.user_id,
        func.sum(Deposit.amount).label("total_deposit")
    ).filter(
        Deposit.status == "approved"
    ).group_by(Deposit.user_id)

    deposit_sums = {row.user_id: row.total_deposit for row in deposit_sums_query}

    # 7. Get all related wallets in one query
    wallet_query = db.query(Wallet).filter(Wallet.user_id.in_(deposit_sums.keys()))
    wallet_map = {wallet.user_id: wallet for wallet in wallet_query}

    # 8. Update each wallet
    for user_id, deposit_sum in deposit_sums.items():
        profit = deposit_sum * profit_rate

        if user_id in wallet_map:
            wallet = wallet_map[user_id]
            wallet.balance = (wallet.balance or 0.0) + profit
            wallet.total_profit = (wallet.total_profit or 0.0) + profit
            wallet.current_profit = (wallet.current_profit or 0.0) + profit
        else:
            # If wallet doesn't exist, create one
            new_wallet = Wallet(
                user_id=user_id,
                balance=profit,
                total_profit=profit,
                current_profit=profit,
            )
            db.add(new_wallet)

    # 9. Commit changes
    db.commit()
    db.refresh(db_admin_profit)

    return db_admin_profit

def update_admin_profit(db: Session, admin_profit_id: int, AdminProfit: AdminProfitUpdate) -> Optional[AdminProfit]:
    db_admin_profit = get_admin_profit(db, admin_profit_id)
    if db_admin_profit:
        update_data = AdminProfit.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_admin_profit, key, value)
        db.commit()
        db.refresh(db_admin_profit)
    return db_admin_profit

def delete_admin_profit(db: Session, admin_profit_id: int) -> bool:
    db_admin_profit = get_admin_profit(db, admin_profit_id)
    if db_admin_profit:
        db.delete(db_admin_profit)
        db.commit()
        return True
    return False