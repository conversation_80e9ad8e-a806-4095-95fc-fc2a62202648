from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.profit_logs import ProfitLogCreate, ProfitLogOut
from app.services.profit_log_service import get_profit_logs, get_profit_log, create_profit_log, delete_profit_log
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/profit_logs",
    tags=["profit_logs"]
)

@router.get("/", response_model=Page[ProfitLogOut])
def read_profit_logs(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_profit_logs(db, params=params)

@router.get("/{profit_log_id}", response_model=ProfitLogOut)
def read_profit_log(profit_log_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    profit_log = get_profit_log(db, profit_log_id=profit_log_id)
    if profit_log is None:
        raise HTTPException(status_code=404, detail="profit_log not found")
    return profit_log

@router.post("/", response_model=ProfitLogOut, status_code=status.HTTP_201_CREATED)
def create_new_profit_log(profit_log: ProfitLogCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_profit_log(db=db, profit_log=profit_log)

# @router.put("/{profit_log_id}", response_model=ProfitLogOut)
# def update_existing_profit_log(profit_log_id: int, profit_log: ProfitLogUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
#     updated_profit_log = update_profit_log(db=db, profit_log_id=profit_log_id, profit_log=profit_log)
#     if updated_profit_log is None:
#         raise HTTPException(status_code=404, detail="profit_log not found")
#     return updated_profit_log

@router.delete("/{profit_log_id}", status_code=status.HTTP_200_OK)
def delete_existing_profit_log(profit_log_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_profit_log(db=db, profit_log_id=profit_log_id)
    if not success:
        raise HTTPException(status_code=404, detail="profit_log not found")
    return {"detail": "profit_log deleted successfully"}