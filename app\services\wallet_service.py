from sqlalchemy.orm import Session
from app.models.wallets import Wallet
from app.schemas.wallets import WalletCreate, WalletUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_wallets(db: Session, params: PaginationParams) -> Page[Wallet]:
    query = db.query(Wallet)
    return paginate(query, params)

def get_wallet(db: Session, wallet_id: int) -> Optional[Wallet]:
    return db.query(Wallet).filter(Wallet.id == wallet_id).first()

def create_wallet(db: Session, user_id, balance, total_referral_bonus, total_profit) -> Wallet:
    db_wallet = Wallet(user_id=user_id,
                       balance=balance,
                       total_referral_bonus=total_referral_bonus,
                       total_profit=total_profit)
    db.add(db_wallet)
    db.commit()
    db.refresh(db_wallet)
    return db_wallet

def update_wallet(db: Session, wallet_id: int, Wallet: WalletUpdate) -> Optional[Wallet]:
    db_wallet = get_wallet(db, wallet_id)
    if db_wallet:
        update_data = Wallet.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_wallet, key, value)
        db.commit()
        db.refresh(db_wallet)
    return db_wallet

def delete_wallet(db: Session, wallet_id: int) -> bool:
    db_wallet = get_wallet(db, wallet_id)
    if db_wallet:
        db.delete(db_wallet)
        db.commit()
        return True
    return False