from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.deposits import DepositCreate, DepositUpdate, DepositOut
from app.services.deposit_service import get_deposits, get_deposit, create_deposit, approve_deposit, delete_deposit
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user, is_admin_user


router = APIRouter(
    prefix="/deposits",
    tags=["deposits"]
)

@router.get("/", response_model=Page[DepositOut])
def read_deposits(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_deposits(db, params=params)

@router.get("/{deposit_id}", response_model=DepositOut)
def read_deposit(deposit_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    deposit = get_deposit(db, deposit_id=deposit_id)
    if deposit is None:
        raise HTTPException(status_code=404, detail="deposit not found")
    return deposit

@router.post("/", response_model=DepositOut, status_code=status.HTTP_201_CREATED)
def create_new_deposit(deposit: DepositCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_deposit(db=db, Deposit_data=deposit, user_id=current_user.id)

@router.post("/{deposit_id}", response_model=DepositOut)
def aprove_pending_deposit(deposit_id: int,status : DepositUpdate, db: Session = Depends(get_db), current_user=Depends(is_admin_user)):
    updated_deposit = approve_deposit(db=db, deposit_id=deposit_id, status=status, current_user_id=current_user.id)
    if updated_deposit is None:
        raise HTTPException(status_code=404, detail="deposit not found")
    return updated_deposit

@router.delete("/{deposit_id}", status_code=status.HTTP_200_OK)
def delete_existing_deposit(deposit_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_deposit(db=db, deposit_id=deposit_id)
    if not success:
        raise HTTPException(status_code=404, detail="deposit not found")
    return {"detail": "deposit deleted successfully"}