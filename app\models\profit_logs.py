from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey



# PROFIT_LOGS Table
class ProfitLog(BaseModel):
    __tablename__ = "profit_logs"
    
    id = Column(Integer, primary_key=True)
    # deposit_id = Column(Integer, ForeignKey("deposits.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    profit_amount = Column(Float, nullable=False)
    profit_percent = Column(Float, nullable=False)
    
    # deposit = relationship("Deposit", back_populates="profit_logs")
    user = relationship("User", back_populates="profit_logs")
