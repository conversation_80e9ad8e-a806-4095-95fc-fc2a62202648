from collections import defaultdict

def build_referral_tree(root_user_id, rows):
    # Map users by id
    user_map = {row["user_id"]: dict(row, legs=[]) for row in rows}

    tree = []

    for row in rows:
        uid = row["user_id"]
        parent_id = row["referrer_id"]
    
        if uid == root_user_id:
            tree.append(user_map[uid])
        elif parent_id in user_map:
            user_map[parent_id]["legs"].append(user_map[uid])


    return tree
