from sqlalchemy.orm import Session
from app.models.deposits import Deposit
from app.schemas.deposits import DepositCreate, DepositUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.models.wallets import Wallet
from datetime import datetime
from app.services.referral_service import admin_approve_deposit_and_process_commissions


def get_deposits(db: Session, params: PaginationParams) -> Page[Deposit]:
    query = db.query(Deposit)
    return paginate(query, params)

def get_deposit(db: Session, deposit_id: int) -> Optional[Deposit]:
    return db.query(Deposit).filter(Deposit.id == deposit_id).first()

def create_deposit(db: Session, Deposit_data: DepositCreate, user_id: int) -> Deposit:
    db_deposit = Deposit(user_id=user_id, **Deposit_data.dict(exclude={"user_id"}))
    db.add(db_deposit)
    db.commit()
    db.refresh(db_deposit)
    return db_deposit

def approve_deposit(db: Session, deposit_id: int, current_user_id: int) -> Optional[Deposit]:
    db_deposit = get_deposit(db, deposit_id)
    # if db_deposit:
        # update_data = Deposit.dict(exclude_unset=True)
        # for key, value in update_data.items():
        #     setattr(db_deposit, key, value)
        # db_deposit.status = deposit.status
        # db_deposit.reviewed_by_admin_id = current_user_id
        # db_deposit.approved_at = datetime.utcnow()
    print("update deposit about to call admin_approve_deposit_and_process_commissions")
    admin_approve = admin_approve_deposit_and_process_commissions(deposit_id=db_deposit.id, db=db, admin_user_id=current_user_id)
    print("update deposit admin_approve_deposit_and_process_commissions returned and before commit", admin_approve)
    db.commit()
    db.refresh(db_deposit)
    print("this is deposit status", db_deposit.status)
    if db_deposit.status == "approved":
        # Update the user's wallet balance if the deposit is approved
        user_wallet = db.query(Wallet).filter(Wallet.user_id == db_deposit.user_id).first()
        if user_wallet:
            user_wallet.balance = (user_wallet.balance or 0.0) + db_deposit.amount
            user_wallet.total_deposits = (user_wallet.total_deposits or 0.0) + db_deposit.amount
            user_wallet.current_deposits = (user_wallet.current_deposits or 0.0) + db_deposit.amount
            db.commit()
            db.refresh(user_wallet)
    return db_deposit

def delete_deposit(db: Session, deposit_id: int) -> bool:
    db_deposit = get_deposit(db, deposit_id)
    if db_deposit:
        db.delete(db_deposit)
        db.commit()
        return True
    return False