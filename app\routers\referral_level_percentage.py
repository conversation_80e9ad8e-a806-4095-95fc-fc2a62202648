from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.referral_level_percentage import ReferralLevelPercentageCreate, ReferralLevelPercentageUpdate, ReferralLevelPercentageOut
from app.services.referral_level_percentage_service import get_referral_level_percentages, get_referral_level_percentage, create_referral_level_percentage, update_referral_level_percentage, delete_referral_level_percentage
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user

router = APIRouter(
    prefix="/referral_level_percentages",
    tags=["referral_level_percentages"]
)

@router.get("/", response_model=Page[ReferralLevelPercentageOut])
def read_referral_level_percentages(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_referral_level_percentages(db, params=params)

@router.get("/{referral_level_percentage_id}", response_model=ReferralLevelPercentageOut)
def read_referral_level_percentage(referral_level_percentage_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    referral_level_percentage = get_referral_level_percentage(db, referral_level_percentage_id=referral_level_percentage_id)
    if referral_level_percentage is None:
        raise HTTPException(status_code=404, detail="referral_level_percentage not found")
    return referral_level_percentage

@router.post("/", response_model=ReferralLevelPercentageOut, status_code=status.HTTP_201_CREATED)
def create_new_referral_level_percentage(referral_level_percentage: ReferralLevelPercentageCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_referral_level_percentage(db=db, referral_level_percentage=referral_level_percentage)

@router.put("/{referral_level_percentage_id}", response_model=ReferralLevelPercentageOut)
def update_existing_referral_level_percentage(referral_level_percentage_id: int, referral_level_percentage: ReferralLevelPercentageUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_referral_level_percentage = update_referral_level_percentage(db=db, referral_level_percentage_id=referral_level_percentage_id, referral_level_percentage=referral_level_percentage)
    if updated_referral_level_percentage is None:
        raise HTTPException(status_code=404, detail="referral_level_percentage not found")
    return updated_referral_level_percentage

@router.delete("/{referral_level_percentage_id}", status_code=status.HTTP_200_OK)
def delete_existing_referral_level_percentage(referral_level_percentage_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_referral_level_percentage(db=db, referral_level_percentage_id=referral_level_percentage_id)
    if not success:
        raise HTTPException(status_code=404, detail="referral_level_percentage not found")
    return {"detail": "referral_level_percentage deleted suceessfully"}