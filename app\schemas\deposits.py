from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class DepositBase(BaseModel):
    admin_wallet_id: str
    amount: float
    transaction_id: str
    status: Optional[str] = "pending"  # plain string, default to "pending"


class DepositCreate(DepositBase):
    pass


class DepositUpdate(BaseModel):
    status: Optional[str] = None


class DepositOut(DepositBase):
    id: int
    user_id: int
    reviewed_by_admin_id: Optional[int] = None
    approved_at: Optional[datetime] = None
    created_at: datetime  # inherited from BaseModel
    updated_at: datetime  # inherited from BaseModel

    class Config:
        from_attributes = True
