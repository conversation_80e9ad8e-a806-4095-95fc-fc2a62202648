from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.withdrawals import WithdrawalCreate, WithdrawalUpdate, WithdrawalOut
from app.services.withdrawal_service import get_withdrawals, get_withdrawal, create_withdrawal, delete_withdrawal, withdrawal_status_update
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user, is_admin_user


router = APIRouter(
    prefix="/withdrawals",
    tags=["withdrawals"]
)

@router.get("/", response_model=Page[WithdrawalOut])
def read_withdrawals(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_withdrawals(db, params=params)

@router.get("/{withdrawal_id}", response_model=WithdrawalOut)
def read_withdrawal(withdrawal_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    withdrawal = get_withdrawal(db, withdrawal_id=withdrawal_id)
    if withdrawal is None:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return withdrawal

@router.post("/", response_model=WithdrawalOut, status_code=status.HTTP_201_CREATED)
def create_new_withdrawal(withdrawal: WithdrawalCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_withdrawal(db=db, withdrawal=withdrawal, user_id=current_user.id)

# @router.put("/{withdrawal_id}", response_model=WithdrawalOut)
# def update_existing_withdrawal(withdrawal_id: int, withdrawal: WithdrawalUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
#     updated_withdrawal = update_withdrawal(db=db, withdrawal_id=withdrawal_id, withdrawal=withdrawal)
#     if updated_withdrawal is None:
#         raise HTTPException(status_code=404, detail="withdrawal not found")
#     return updated_withdrawal

@router.delete("/{withdrawal_id}", status_code=status.HTTP_200_OK)
def delete_existing_withdrawal(withdrawal_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_withdrawal(db=db, withdrawal_id=withdrawal_id)
    if not success:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return {"detail": "withdrawal deleted successfully"}


@router.post("/{withdrawal_id}/withdrawal_status", response_model=WithdrawalOut)
def update_withdrawal_status(withdrawal_id: int, withdrawal: WithdrawalUpdate, db: Session = Depends(get_db), current_user=Depends(is_admin_user)):
    updated_withdrawal = withdrawal_status_update(db=db, withdrawal_id=withdrawal_id, withdrawal=withdrawal)
    if updated_withdrawal is None:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return updated_withdrawal