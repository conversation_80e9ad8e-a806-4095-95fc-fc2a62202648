from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel


class CurrencyConversionBase(PydanticBaseModel):
    from_currency: str
    code: str
    conversion_rate: float
    is_active: bool = True


class CurrencyConversionCreate(CurrencyConversionBase):
    pass


class CurrencyConversionUpdate(PydanticBaseModel):
    from_currency: Optional[str] = None
    code: Optional[str] = None
    conversion_rate: Optional[float] = None
    is_active: Optional[bool] = None


class CurrencyConversionOut(CurrencyConversionBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
