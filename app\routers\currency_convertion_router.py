from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.currency_convertion import CurrencyConversionCreate, CurrencyConversionUpdate, CurrencyConversionOut
from app.services.currency_convertion_service import get_currency_conversions, get_currency_conversion, create_currency_conversion, update_currency_conversion, delete_currency_conversion
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/currency_conversions",
    tags=["currency_conversions"]
)

@router.get("/", response_model=Page[CurrencyConversionOut])
def read_currency_conversions(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_currency_conversions(db, params=params)

@router.get("/{currency_conversion_id}", response_model=CurrencyConversionOut)
def read_currency_conversion(currency_conversion_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    currency_conversion = get_currency_conversion(db, currency_conversion_id=currency_conversion_id)
    if currency_conversion is None:
        raise HTTPException(status_code=404, detail="currency_conversion not found")
    return currency_conversion

@router.post("/", response_model=CurrencyConversionOut, status_code=status.HTTP_201_CREATED)
def create_new_currency_conversion(currency_conversion: CurrencyConversionCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_currency_conversion(db=db, currency_conversion=currency_conversion)

@router.put("/{currency_conversion_id}", response_model=CurrencyConversionOut)
def update_existing_currency_conversion(currency_conversion_id: int, currency_conversion: CurrencyConversionUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_currency_conversion = update_currency_conversion(db=db, currency_conversion_id=currency_conversion_id, currency_conversion=currency_conversion)
    if updated_currency_conversion is None:
        raise HTTPException(status_code=404, detail="currency_conversion not found")
    return updated_currency_conversion

@router.delete("/{currency_conversion_id}", status_code=status.HTTP_200_OK)
def delete_existing_currency_conversion(currency_conversion_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_currency_conversion(db=db, currency_conversion_id=currency_conversion_id)
    if not success:
        raise HTTPException(status_code=404, detail="currency_conversion not found")
    return {"detail": "currency_conversion deleted successfully"}