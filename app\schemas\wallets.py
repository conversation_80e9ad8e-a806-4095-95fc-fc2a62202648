from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum



class WalletBase(PydanticBaseModel):
    user_id: int
    balance: float = 0.0
    total_profit: float = 0.0
    total_referral_bonus: float = 0.0

class WalletCreate(WalletBase):
    pass

class WalletUpdate(PydanticBaseModel):
    balance: Optional[float] = None
    total_profit: Optional[float] = None
    total_referral_bonus: Optional[float] = None

class WalletOut(WalletBase):
    class Config:
        orm_mode = True