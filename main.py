from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
# from .app.router import album_router
from app.routers import (admin_profit_router,
                         admin_wallet_router,
                         currency_convertion_router,
                         deposit_router,
                         deposite_slab_router,
                         privacy_and_policy_router,
                         product_router,
                         profit_log_router,
                         referral_level_percentage,
                         terms_and_condition_router,
                         transaction_log_router,
                         users_router,
                         wallet_router,
                         withdrawal_router)





app=FastAPI()


# CORS Configuration
origins = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3000",
    "http://*************:3000",  # Add your network IP
    "*"  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)



app.include_router(admin_profit_router.router)
app.include_router(admin_wallet_router.router)
app.include_router(currency_convertion_router.router)
app.include_router(deposit_router.router)
app.include_router(deposite_slab_router.router)
app.include_router(privacy_and_policy_router.router)
app.include_router(product_router.router)
app.include_router(profit_log_router.router)
app.include_router(referral_level_percentage.router)
app.include_router(terms_and_condition_router.router)
app.include_router(transaction_log_router.router)
app.include_router(users_router.router)
app.include_router(wallet_router.router)
app.include_router(withdrawal_router.router)



# this is to seed initial referral levels into db when the application starts.


# from fastapi import FastAPI, Depends
# from sqlalchemy.orm import Session

# # Adjust imports based on your project structure
# from app.config.db import SessionLocal # Or however you get your DB Session factory
# from app.utils.initial_seeding_data import create_initial_referral_levels # The function we just defined
# from app.config.db import Base # If you need to create tables (usually for dev)
# from app.config.db import engine # If you need to create tables (usually for dev)


# This is just an example, you might have a more sophisticated way to create tables.
# In production, migrations (like Alembic) are preferred over create_all.
# def create_tables():
#     print("Creating database tables if they don't exist...")
#     Base.metadata.create_all(bind=engine)

# app = FastAPI()

# @app.on_event("startup")
# async def on_startup():
#     print("Application startup event triggered.")
#     # create_tables() # Call this only if you want FastAPI to create tables (e.g., for local dev with SQLite)

#     db = SessionLocal() # Create a new session instance for the startup event
#     try:
#         print("--- Running initial data setup ---")
#         print("Warning: Database configuration and migrations of the model or tables should be done before starting the application since initial referral level model data seeding is in process")
#         create_initial_referral_levels(db=db)
#         print("--- Initial data setup finished ---")
#     finally:
#         db.close() # Always close the session

# # ... your other FastAPI routes and configurations ...

# # Example endpoint (not related to seeding, just for context)
# @app.get("/")
# async def read_root():
#     return {"message": "Welcome to the application!"}