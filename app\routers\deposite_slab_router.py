from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.deposite_slab import DepositSlabCreate, DepositSlabUpdate, DepositSlabOut
from app.services.deposite_slab_service import get_deposit_slabs, get_deposit_slab, create_deposit_slab, update_deposit_slab, delete_deposit_slab
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/deposite_slabs",
    tags=["deposite_slabs"]
)

@router.get("/", response_model=Page[DepositSlabOut])
def read_deposite_slabs(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_deposit_slabs(db, params=params)

@router.get("/{deposite_slab_id}", response_model=DepositSlabOut)
def read_deposite_slab(deposite_slab_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    deposite_slab = get_deposit_slab(db, deposite_slab_id=deposite_slab_id)
    if deposite_slab is None:
        raise HTTPException(status_code=404, detail="deposite_slab not found")
    return deposite_slab

@router.post("/", response_model=DepositSlabOut, status_code=status.HTTP_201_CREATED)
def create_new_deposite_slab(deposite_slab: DepositSlabCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_deposit_slab(db=db, deposite_slab=deposite_slab)

@router.put("/{deposite_slab_id}", response_model=DepositSlabOut)
def update_existing_deposite_slab(deposite_slab_id: int, deposite_slab: DepositSlabUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_deposite_slab = update_deposit_slab(db=db, deposite_slab_id=deposite_slab_id, deposite_slab=deposite_slab)
    if updated_deposite_slab is None:
        raise HTTPException(status_code=404, detail="deposite_slab not found")
    return updated_deposite_slab

@router.delete("/{deposite_slab_id}", status_code=status.HTTP_200_OK)
def delete_existing_deposite_slab(deposite_slab_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_deposit_slab(db=db, deposite_slab_id=deposite_slab_id)
    if not success:
        raise HTTPException(status_code=404, detail="deposite_slab not found")
    return {"detail": "deposite_slab deleted successfully"}