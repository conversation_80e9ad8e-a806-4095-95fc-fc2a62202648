"""modified status field in deposit model to use indexing and enum

Revision ID: 06efa559bf0a
Revises: 1ed2ac0185df
Create Date: 2025-05-23 12:38:01.098844

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '06efa559bf0a'
down_revision: Union[str, None] = '1ed2ac0185df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('deposits', 'status',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'WITHDRAWN', name='depositstatus'),
               nullable=True)
    op.create_index(op.f('ix_deposits_status'), 'deposits', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_deposits_status'), table_name='deposits')
    op.alter_column('deposits', 'status',
               existing_type=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'WITHDRAWN', name='depositstatus'),
               type_=mysql.VARCHAR(length=100),
               nullable=False)
    # ### end Alembic commands ###
