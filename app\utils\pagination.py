from typing import <PERSON>Var, Generic, List, Optional, Dict, Any
from sqlalchemy.orm import Query
from fastapi import Query as QueryParam, Depends
from pydantic import BaseModel
from math import ceil

T = TypeVar('T')

class PaginationParams:
    def __init__(
        self,
        skip: int = QueryParam(0, ge=0, description="Number of items to skip"),
        limit: int = QueryParam(10, ge=1, le=100, description="Number of items to return"),
    ):
        self.skip = skip
        self.limit = limit

class Page(BaseModel, Generic[T]):
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool
    items: List[T]

    class Config:
        arbitrary_types_allowed = True

def paginate(query: Query, params: PaginationParams) -> Dict[str, Any]:
    """
    Apply skip-limit pagination to a SQLAlchemy query.

    Args:
        query: SQLAlchemy query object
        params: PaginationParams object with skip and limit attributes

    Returns:
        Dictionary with pagination metadata and items
    """
    total = query.count()
    items = query.offset(params.skip).limit(params.limit).all()

    return Page(
        total=total,
        skip=params.skip,
        limit=params.limit,
        has_next=(params.skip + params.limit) < total,
        has_prev=params.skip > 0,
        items=items,
    )