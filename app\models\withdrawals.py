from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey


# WITHDRAWALS Table
class Withdrawal(BaseModel):
    __tablename__ = "withdrawals"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    wallet_address = Column(String(100), nullable=False)
    amount = Column(Float, nullable=False)
    status = Column(String(100), nullable=False)
    approved_at = Column(DateTime)
    network = Column(String(100), nullable=False)
    
    user = relationship("User", back_populates="withdrawals")
