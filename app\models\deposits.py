from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
from enum import Enum
from sqlalchemy import Column, Enum as SQLAlchemyEnum


class DepositStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"



# DEPOSITS Table
class Deposit(BaseModel):
    __tablename__ = "deposits"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    admin_wallet_id = Column(String(200), index=True, nullable=False)
    amount = Column(Float, nullable=False)
    transaction_id = Column(Text, nullable=False)
    reviewed_by_admin_id = Column(Integer, ForeignKey("users.id"))
    status = Column(SQLAlchemyEnum(DepositStatus), default=DepositStatus.PENDING, index=True) # "pending", "approved", "rejected", "withdrawn"
    approved_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="deposits", foreign_keys=[user_id])
    # admin_wallet = relationship("AdminWallet", back_populates="deposits")
    # profit_logs = relationship("ProfitLog", back_populates="deposit")
    # referral_commissions = relationship("ReferralCommission", back_populates="deposit")
    referral_commissions = relationship("ReferralCommission", back_populates="deposit_source")
