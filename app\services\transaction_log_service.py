from sqlalchemy.orm import Session
from app.models.transaction_logs import TransactionLedger
from app.schemas.transaction_logs import TransactionLogCreate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_transaction_logs(db: Session, params: PaginationParams) -> Page[TransactionLedger]:
    query = db.query(TransactionLedger)
    return paginate(query, params)

def get_transaction_log(db: Session, transaction_log_id: int) -> Optional[TransactionLedger]:
    return db.query(TransactionLedger).filter(TransactionLedger.id == transaction_log_id).first()

def create_transaction_log(db: Session, transaction_log: TransactionLogCreate) -> TransactionLedger:
    db_transaction_log = transaction_log(**transaction_log.dict())
    db.add(db_transaction_log)
    db.commit()
    db.refresh(db_transaction_log)
    return db_transaction_log

# def update_transaction_log(db: Session, transaction_log_id: int, transaction_log: TransactionLogUpdate) -> Optional[transaction_log]:
#     db_transaction_log = get_transaction_log(db, transaction_log_id)
#     if db_transaction_log:
#         update_data = transaction_log.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_transaction_log, key, value)
#         db.commit()
#         db.refresh(db_transaction_log)
#     return db_transaction_log

def delete_transaction_log(db: Session, transaction_log_id: int) -> bool:
    db_transaction_log = get_transaction_log(db, transaction_log_id)
    if db_transaction_log:
        db.delete(db_transaction_log)
        db.commit()
        return True
    return False