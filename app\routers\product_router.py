from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.products import ProductCreate, ProductUpdate, ProductOut
from app.services.product_service import get_products, get_product, create_product, update_product, delete_product
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/products",
    tags=["products"]
)

@router.get("/", response_model=Page[ProductOut])
def read_products(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_products(db, params=params)

@router.get("/{product_id}", response_model=ProductOut)
def read_product(product_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    product = get_product(db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="Product not found")
    return product

@router.post("/", response_model=ProductOut, status_code=status.HTTP_201_CREATED)
def create_new_product(product: ProductCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_product(db=db, product=product)

@router.put("/{product_id}", response_model=ProductOut)
def update_existing_product(product_id: int, product: ProductUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_product = update_product(db=db, product_id=product_id, product=product)
    if updated_product is None:
        raise HTTPException(status_code=404, detail="Product not found")
    return updated_product

@router.delete("/{product_id}", status_code=status.HTTP_200_OK)
def delete_existing_product(product_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_product(db=db, product_id=product_id)
    if not success:
        raise HTTPException(status_code=404, detail="Product not found")
    return {"detail": "Product deleted successfully"}