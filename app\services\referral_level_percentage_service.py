from sqlalchemy.orm import Session
from app.models.referral_percentage import ReferralLevelPercentage
from app.schemas.referral_level_percentage import ReferralLevelPercentageCreate, ReferralLevelPercentageUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_referral_level_percentages(db: Session, params: PaginationParams) -> Page[ReferralLevelPercentage]:
    query = db.query(ReferralLevelPercentage)
    return paginate(query, params)

def get_referral_level_percentage(db: Session, referral_level_percentage_id: int) -> Optional[ReferralLevelPercentage]:
    return db.query(ReferralLevelPercentage).filter(ReferralLevelPercentage.id == referral_level_percentage_id).first()

def create_referral_level_percentage(db: Session, ReferralLevelPercentage: ReferralLevelPercentageCreate) -> ReferralLevelPercentage:
    db_referral_level_percentage = ReferralLevelPercentage(**ReferralLevelPercentage.dict())
    db.add(db_referral_level_percentage)
    db.commit()
    db.refresh(db_referral_level_percentage)
    return db_referral_level_percentage

def update_referral_level_percentage(db: Session, referral_level_percentage_id: int, ReferralLevelPercentage: ReferralLevelPercentageUpdate) -> Optional[ReferralLevelPercentage]:
    db_referral_level_percentage = get_referral_level_percentage(db, referral_level_percentage_id)
    if db_referral_level_percentage:
        update_data = ReferralLevelPercentage.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_referral_level_percentage, key, value)
        db.commit()
        db.refresh(db_referral_level_percentage)
    return db_referral_level_percentage

def delete_referral_level_percentage(db: Session, referral_level_percentage_id: int) -> bool:
    db_referral_level_percentage = get_referral_level_percentage(db, referral_level_percentage_id)
    if db_referral_level_percentage:
        db.delete(db_referral_level_percentage)
        db.commit()
        return True
    return False